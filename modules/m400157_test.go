package modules

import (
	"fmt"
	"igameCommon/utils"
	"math/rand"
	"os"
	"testing"
)

// 简化的棋盘处理器 - 专注核心性能优化
type simpleGridProcessor struct {
	config       *c400157
	randByWeight *utils.RandomWeightPicker[int16, int32]
	gridBuffer   []int16
	colBuffers   [][]int
}

func newSimpleGridProcessor(config *c400157, randByWeight *utils.RandomWeightPicker[int16, int32]) *simpleGridProcessor {
	gridSize := config.Row * config.Column
	processor := &simpleGridProcessor{
		config:       config,
		randByWeight: randByWeight,
		gridBuffer:   make([]int16, gridSize),
		colBuffers:   make([][]int, config.Column),
	}

	// 预分配列缓冲区
	for i := range processor.colBuffers {
		processor.colBuffers[i] = make([]int, config.Row)
	}

	return processor
}

// 简化的网格生成 - 使用批量生成减少函数调用
func (p *simpleGridProcessor) generateGrid(rd *rand.Rand) []int16 {
	gridSize := p.config.Row * p.config.Column
	grid := p.randByWeight.More(gridSize, rd)
	copy(p.gridBuffer, grid)
	return p.gridBuffer
}

// 简化的列转换 - 直接操作，避免append
func (p *simpleGridProcessor) toColumns(grid []int16) [][]int {
	for col := 0; col < p.config.Column; col++ {
		colBuffer := p.colBuffers[col]
		for row := 0; row < p.config.Row; row++ {
			colBuffer[row] = int(grid[row*p.config.Column+col])
		}
	}
	return p.colBuffers
}

// 简化的Wild处理 - 合并重复逻辑
func (p *simpleGridProcessor) processWild(gridCol [][]int) {
	wildIcon := int(p.config.WildIcon)

	for col := 0; col < p.config.Column; col++ {
		column := gridCol[col]
		rowCount := len(column)

		// 合并所有Wild处理逻辑到一个循环中
		for row := 0; row < rowCount; row++ {
			if column[row] == wildIcon {
				// 简化条件判断
				switch row {
				case 0:
					if rowCount > 1 {
						// 处理第一行Wild
					}
				case 1:
					if column[0] != wildIcon && row+1 < rowCount {
						column[row+1] = 0
					}
				case 2:
					if column[rowCount-1] != wildIcon && row-1 >= 0 {
						column[row-1] = 0
					}
				case rowCount - 1:
					if rowCount > 1 {
						// 处理最后一行Wild
					}
				}
			}
		}
	}
}

func TestM400157(t *testing.T) {
	config, _ := os.ReadFile("../bin/configs/400157.yaml")
	m := m400157{}
	m.Init(config)

	// 创建简化的处理器
	processor := newSimpleGridProcessor(&m.Config, m.RandByWeight)

	for i := range [1000]int{} {
		fmt.Println(i)
		rd := rand.New(rand.NewSource(int64(i)))

		// 简化：直接使用优化的网格生成
		grid := processor.generateGrid(rd)

		// 简化：减少打印循环的复杂性
		gridSize := len(grid)
		for i := 0; i < gridSize; i += 5 {
			end := i + 5
			if end > gridSize {
				end = gridSize
			}
			fmt.Println(grid[i:end])
		}
		fmt.Println("===============")

		// 简化：使用优化的列转换
		gridCol := processor.toColumns(grid)

		// 简化：使用合并的Wild处理
		processor.processWild(gridCol)

		for _, col := range gridCol {
			fmt.Println(col)
		}
	}
}

// 简化的基准测试 - 专注核心性能指标
func BenchmarkOriginalGridProcessing(b *testing.B) {
	config, _ := os.ReadFile("../bin/configs/400157.yaml")
	m := m400157{}
	m.Init(config)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		rd := rand.New(rand.NewSource(int64(i)))

		// 原始方式：逐个生成 + 多次分配
		gridSize := m.Config.Row * m.Config.Column
		grid := make([]int16, gridSize)
		for row := 0; row < m.Config.Row; row++ {
			for col := 0; col < m.Config.Column; col++ {
				index := row*m.Config.Column + col
				grid[index] = m.RandByWeight.One(rd)
			}
		}

		// 原始方式：使用append转换列
		var gridCol [][]int
		for col := 0; col < m.Config.Column; col++ {
			tmp := make([]int, 0)
			for row := 0; row < m.Config.Row; row++ {
				tmp = append(tmp, int(grid[row*m.Config.Column+col]))
			}
			gridCol = append(gridCol, tmp)
		}
	}
}

func BenchmarkOptimizedGridProcessing(b *testing.B) {
	config, _ := os.ReadFile("../bin/configs/400157.yaml")
	m := m400157{}
	m.Init(config)

	processor := newSimpleGridProcessor(&m.Config, m.RandByWeight)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		rd := rand.New(rand.NewSource(int64(i)))

		// 优化方式：批量生成 + 预分配缓冲区
		grid := processor.generateGrid(rd)
		gridCol := processor.toColumns(grid)
		processor.processWild(gridCol)
	}
}
