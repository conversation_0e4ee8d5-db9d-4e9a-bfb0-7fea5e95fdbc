package modules

import (
	"fmt"
	"igameCommon/utils"
	"math/rand"
	"os"
	"testing"
)

// 优化后的棋盘处理器，减少内存分配和提高性能
type optimizedGridProcessor struct {
	config       *c400157
	randByWeight *utils.RandomWeightPicker[int16, int32]

	// 预分配的缓冲区，避免重复分配
	gridBuffer    []int16
	gridColBuffer [][]int
	tempBuffer    []int
}

func newOptimizedGridProcessor(config *c400157, randByWeight *utils.RandomWeightPicker[int16, int32]) *optimizedGridProcessor {
	gridSize := config.Row * config.Column
	processor := &optimizedGridProcessor{
		config:        config,
		randByWeight:  randByWeight,
		gridBuffer:    make([]int16, gridSize),
		gridColBuffer: make([][]int, config.Column),
		tempBuffer:    make([]int, config.Row),
	}

	// 预分配列缓冲区
	for i := range processor.gridColBuffer {
		processor.gridColBuffer[i] = make([]int, config.Row)
	}

	return processor
}

// 优化的网格生成方法
func (p *optimizedGridProcessor) generateOptimizedGrid(rd *rand.Rand) []int16 {
	gridSize := p.config.Row * p.config.Column

	// 批量生成随机数，减少函数调用开销
	grid := p.randByWeight.More(gridSize, rd)
	copy(p.gridBuffer, grid)

	return p.gridBuffer
}

// 优化的列转换方法，避免重复分配
func (p *optimizedGridProcessor) convertToColumns(grid []int16) [][]int {
	// 重置列缓冲区而不是重新分配
	for col := 0; col < p.config.Column; col++ {
		column := p.gridColBuffer[col]
		for row := 0; row < p.config.Row; row++ {
			column[row] = int(grid[row*p.config.Column+col])
		}
	}
	return p.gridColBuffer
}

// 优化的Wild处理逻辑，减少重复判断
func (p *optimizedGridProcessor) processWildOptimized(gridCol [][]int) {
	wildIcon := int(p.config.WildIcon)

	for col := 0; col < p.config.Column; col++ {
		column := gridCol[col]
		rowCount := len(column)

		// 第一轮处理：处理中间位置的Wild
		for row := 1; row < rowCount-1; row++ {
			if column[row] == wildIcon {
				// 检查是否需要在相邻位置放置0
				if row == 1 && column[0] != wildIcon && row+1 < rowCount {
					column[row+1] = 0
				} else if row == 2 && column[rowCount-1] != wildIcon && row-1 >= 0 {
					column[row-1] = 0
				}
			}
		}

		// 第二轮处理：处理边界位置的Wild
		if rowCount > 0 {
			// 处理最后一行的Wild
			if column[rowCount-1] == wildIcon {
				// 需要扩展数组，但这里我们先标记
				if rowCount < p.config.Row+1 {
					// 这里需要特殊处理，因为涉及到数组扩展
					// 为了保持性能，我们使用临时缓冲区
					copy(p.tempBuffer[:rowCount], column[:rowCount])
					p.tempBuffer[rowCount] = 0
					if rowCount+1 > p.config.Row {
						copy(column, p.tempBuffer[1:p.config.Row+1])
					} else {
						copy(column, p.tempBuffer[:rowCount+1])
					}
				}
			}

			// 处理第一行的Wild
			if column[0] == wildIcon {
				// 需要在前面插入0
				if rowCount < p.config.Row+1 {
					copy(p.tempBuffer[1:rowCount+1], column[:rowCount])
					p.tempBuffer[0] = 0
					if rowCount+1 > p.config.Row {
						copy(column, p.tempBuffer[:p.config.Row])
					} else {
						copy(column, p.tempBuffer[:rowCount+1])
					}
				}
			}
		}
	}
}

func TestM400157(t *testing.T) {
	config, _ := os.ReadFile("../bin/configs/400157.yaml")
	m := m400157{}
	m.Init(config)

	// 创建优化的处理器
	processor := newOptimizedGridProcessor(&m.Config, m.RandByWeight)

	for i := range [1000]int{} {
		fmt.Println(i)
		rd := rand.New(rand.NewSource(int64(i)))

		// 使用优化的网格生成
		grid := processor.generateOptimizedGrid(rd)

		// 打印网格（每5个一行）
		gridSize := m.Config.Row * m.Config.Column
		for i := 0; i < gridSize; i += 5 {
			end := i + 5
			if end > gridSize {
				end = gridSize
			}
			fmt.Println(grid[i:end])
		}
		fmt.Println("===============")

		// 使用优化的列转换
		gridCol := processor.convertToColumns(grid)

		// 使用优化的Wild处理
		processor.processWildOptimized(gridCol)

		for c := range gridCol {
			fmt.Println(gridCol[c])
		}
	}
}

// 基准测试：原始实现
func BenchmarkOriginalGridProcessing(b *testing.B) {
	config, _ := os.ReadFile("../bin/configs/400157.yaml")
	m := m400157{}
	m.Init(config)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		rd := rand.New(rand.NewSource(int64(i)))
		gridSize := m.Config.Row * m.Config.Column
		grid := make([]int16, gridSize)

		// 原始的网格生成
		for row := 0; row < m.Config.Row; row++ {
			for col := 0; col < m.Config.Column; col++ {
				index := row*m.Config.Column + col
				grid[index] = m.RandByWeight.One(rd)
			}
		}

		// 原始的列转换
		var gridCol [][]int
		for col := 0; col < m.Config.Column; col++ {
			tmp := make([]int, 0)
			for row := 0; row < m.Config.Row; row++ {
				tmp = append(tmp, int(grid[row*m.Config.Column+col]))
			}
			gridCol = append(gridCol, tmp)
		}

		// 原始的Wild处理（简化版，避免数组扩展的复杂性）
		for c := range gridCol {
			for r := range gridCol[c] {
				if r == 1 && gridCol[c][0] != int(m.Config.WildIcon) {
					if gridCol[c][r] == int(m.Config.WildIcon) {
						if r+1 < len(gridCol[c]) {
							gridCol[c][r+1] = 0
						}
					}
				} else if r == 2 && gridCol[c][len(gridCol[c])-1] != int(m.Config.WildIcon) {
					if gridCol[c][r] == int(m.Config.WildIcon) {
						if r-1 >= 0 {
							gridCol[c][r-1] = 0
						}
					}
				}
			}
		}
	}
}

// 基准测试：优化后的实现
func BenchmarkOptimizedGridProcessing(b *testing.B) {
	config, _ := os.ReadFile("../bin/configs/400157.yaml")
	m := m400157{}
	m.Init(config)

	processor := newOptimizedGridProcessor(&m.Config, m.RandByWeight)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		rd := rand.New(rand.NewSource(int64(i)))

		// 优化的网格生成
		grid := processor.generateOptimizedGrid(rd)

		// 优化的列转换
		gridCol := processor.convertToColumns(grid)

		// 优化的Wild处理
		processor.processWildOptimized(gridCol)
	}
}

// 内存分配基准测试：原始实现
func BenchmarkOriginalMemoryAllocation(b *testing.B) {
	config, _ := os.ReadFile("../bin/configs/400157.yaml")
	m := m400157{}
	m.Init(config)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		gridSize := m.Config.Row * m.Config.Column
		grid := make([]int16, gridSize)

		var gridCol [][]int
		for col := 0; col < m.Config.Column; col++ {
			tmp := make([]int, 0)
			for row := 0; row < m.Config.Row; row++ {
				tmp = append(tmp, int(grid[row*m.Config.Column+col]))
			}
			gridCol = append(gridCol, tmp)
		}
	}
}

// 内存分配基准测试：优化后的实现
func BenchmarkOptimizedMemoryAllocation(b *testing.B) {
	config, _ := os.ReadFile("../bin/configs/400157.yaml")
	m := m400157{}
	m.Init(config)

	processor := newOptimizedGridProcessor(&m.Config, m.RandByWeight)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		gridSize := m.Config.Row * m.Config.Column
		grid := make([]int16, gridSize)

		// 使用预分配的缓冲区
		gridCol := processor.convertToColumns(grid)
		_ = gridCol
	}
}
